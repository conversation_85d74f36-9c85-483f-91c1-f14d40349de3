'use server'

import { cookies } from "next/headers";

export async function Register(prevState: any, formData: FormData) {
    const compID = formData.get("compID") as string;
    const fName = formData.get("fName") as string;
    const lName = formData.get("lName") as string;
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;
    const cPassword = formData.get("confirmPassword") as string;

    if (!compID || !password || !cPassword || !fName || !lName) {
        console.log("NISUDNA GYUD !!!" + compID + " " + password + " " + fName + " " + lName);
        return {
            error: "All fields are required!",
            data: { email, password, cPassword, fName, lName, compID }
        }
    }

    if (password !== cPassword) {
        console.log("pasword not match");
        return {
            error: "Passwords does not match!",
            data: { email, password, cPassword, fName, lName, compID }
        }
    }

    console.log("PORT PORT PORT PORT: " + process.env.NEXT_PUBLIC_BACKEND_URL)
    try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/user/register`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                compID: compID,
                fName: fName,
                lName: lName,
                email: email,
                password: password,
                cPassword: cPassword,
            })
        })

        const responseJson = await response.json();
        if (response.status.toString() === "201") {
            return {
                success: responseJson.message
            }
        } else {
            return {
                error: responseJson.error,
                data: { email, password, cPassword, fName, lName, compID }
            }
        }
    } catch (ex) {
        return {
            error: ex,
            data: { email, password, cPassword, fName, lName, compID }
        }
    }
}

export async function Login(prevState: any, formData: FormData) {
    const compID = formData.get("compID") as string;
    const password = formData.get("password") as string;

    if (!compID || !password) {
        return {
            error: "All fields are required!",
            data: { password, compID }
        }
    }

    try {
        console.log("PORT PORT PORT " + process.env.NEXT_PUBLIC_BACKEND_URL);
        const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/user/login`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                compID: compID,
                password: password,
            })
        })

        const responseJson = await response.json();
        const cookieStore = await cookies()
        cookieStore.set("session", responseJson.session.token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
        })
        if (response.status.toString() === "201") {
            return {
                success: responseJson.message,
                data: { password, compID }
            }
        } else {
            return {
                error: responseJson.error,
                data: { password, compID }
            }
        }
    } catch (ex) {
        return {
            error: ex,
            data: { password, compID }
        }
    }
}