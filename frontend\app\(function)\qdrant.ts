export async function AddQdrant() {
    const res = await fetch('/api/qdrant', {
        method: 'POST',
    });
    if (!res.ok) throw new Error('Failed to add Qdrant data');
    return await res.json();
}

export async function GetQdrantData() {
    const res = await fetch('/api/qdrant', {
        method: 'GET',
    });
    if (!res.ok) throw new Error('Failed to get Qdrant data');
    return await res.json();
}

export async function DeleteQdrantCollection() {
    const res = await fetch('/api/qdrant', {
        method: 'DELETE',
    });
    if (!res.ok) throw new Error('Failed to delete Qdrant collection');
    return await res.json();
}