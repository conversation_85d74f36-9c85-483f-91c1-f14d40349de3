import express from "express";
import { AddProduct, DeleteProduct, EditProduct, GetAllProducts } from "../controller/productController";
import multer from "multer";

const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });

router.post("/addproduct", upload.single("productImage"), AddProduct);
router.post("/editproduct", express.json(), EditProduct);
router.post("/deleteproduct", express.json(), DeleteProduct);
router.get("/getallproducts", GetAllProducts);

export default router;
