'use client'

import { <PERSON><PERSON> } from "@/components/ui/button";
import { AddQdrant, GetQdrantData, DeleteQdrantCollection } from "./(function)/qdrant";

export default function LandingPage() {
  return (
    <div className="flex items-center justify-center w-full h-screen text-2xl">
      <PERSON><PERSON><PERSON>
      <Button onClick={async () => {
        try {
          const addResult = await AddQdrant();
          console.log('AddQdrant result:', addResult);
          const getResult = await GetQdrantData();
          console.log('GetQdrantData result:', getResult);
        } catch (err) {
          console.error('Qdrant API error:', err);
        }
      }}>Qdrant Test</Button>
      <Button className="ml-4" variant="destructive" onClick={async () => {
        try {
          const delResult = await DeleteQdrantCollection();
          console.log('DeleteQdrantCollection result:', delResult);
        } catch (err) {
          console.error('Delete Qdrant error:', err);
        }
      }}>Delete Qdrant Collection</Button>
    </div>
  );
}
