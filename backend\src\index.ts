import express from "express";
import cors from "cors";
import cookieParser from "cookie-parser";
import userRouter from "./router/userRouter";
import productRouter from "./router/productRouter"

const app = express();
app.use(
    cors({
        origin: "http://localhost:3000", // Use environment variable or fallback
        methods: ["GET", "POST", "PUT", "DELETE"],
        allowedHeaders: ["Content-Type", "Authorization"],
        credentials: true,
        exposedHeaders: ["Set-Cookie"],
    })
);
app.use(cookieParser());
app.use(express.json()); // Also handle JSON requests
// app.use(upload.none());
const PORT = process.env.PORT || 3001;

app.use("/api/user", userRouter);
app.use("/api/product", productRouter);

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});


