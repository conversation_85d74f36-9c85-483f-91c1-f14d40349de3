"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshToken = exports.verifyToken = exports.generateToken = void 0;
// src/utils/jwt.ts
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
// Ensure you have a JWT_SECRET in your .env file
// And make sure your environment variables are loaded (e.g., using 'dotenv' package)
const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
    console.error('FATAL ERROR: JWT_SECRET is not defined. Please set it in your .env file.');
    // In a real application, you might want to exit the process or throw an error
    // process.exit(1);
}
// Optionally, define an expiration time for your tokens
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1d'; // e.g., '1h', '7d'
const generateToken = (userId, email) => {
    if (!JWT_SECRET) {
        throw new Error('JWT_SECRET is not defined.');
    }
    const payload = { userId, email };
    return jsonwebtoken_1.default.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
};
exports.generateToken = generateToken;
const verifyToken = (token) => {
    if (!JWT_SECRET) {
        throw new Error('JWT_SECRET is not defined.');
    }
    try {
        const decoded = jsonwebtoken_1.default.verify(token, JWT_SECRET);
        return decoded;
    }
    catch (error) {
        console.error('JWT verification failed:', error);
        return null;
    }
};
exports.verifyToken = verifyToken;
const refreshToken = (token) => {
    const payload = (0, exports.verifyToken)(token);
    if (!payload) {
        return null;
    }
    return (0, exports.generateToken)(payload.userId, payload.email);
};
exports.refreshToken = refreshToken;
