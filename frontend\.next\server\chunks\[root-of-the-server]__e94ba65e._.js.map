{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/sapwirsnackies2%20-%20Copy/frontend/app/api/qdrant/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { QdrantClient } from '@qdrant/js-client-rest';\r\n\r\n// If your Qdrant server is running with HTTPS, use api<PERSON>ey and https://host\r\n// If your Qdrant server is running locally with HTTP, do NOT use api<PERSON>ey and use only host/port\r\n// Uncomment the correct config for your setup:\r\n\r\n// For HTTPS Qdrant (e.g., cloud):\r\n// const client = new QdrantClient({\r\n//   url: 'https://your-qdrant-host',\r\n//   apiKey: 'your_api_key',\r\n//   checkCompatibility: false,\r\n// });\r\n\r\n// For local HTTP Qdrant (no apiKey, no SSL):\r\nconst client = new QdrantClient({\r\n  url: \"http://*************:20311/qdrant/api/gateway\",\r\n  // host: 'localhost',\r\n  // port: 6333,\r\n  apiKey: \"SampleQdrantKey2025\",\r\n  checkCompatibility: false,\r\n});\r\n\r\nexport async function POST(req: NextRequest) {\r\n  try {\r\n    try {\r\n      await client.getCollection('test_collection');\r\n    } catch (error: any) {\r\n      if (error.status === 404) {\r\n        await client.createCollection('test_collection', {\r\n          vectors: { size: 4, distance: 'Dot' },\r\n        });\r\n      } else {\r\n        console.error('Qdrant POST inner error:', error);\r\n        throw error;\r\n      }\r\n    }\r\n    const operationInfo = await client.upsert('test_collection', {\r\n      wait: true,\r\n      points: [\r\n        { id: 1, vector: [0.05, 0.61, 0.76, 0.74], payload: { city: 'Berlin' } },\r\n        { id: 2, vector: [0.19, 0.81, 0.75, 0.11], payload: { city: 'London' } },\r\n        { id: 3, vector: [0.36, 0.55, 0.47, 0.94], payload: { city: 'Moscow' } },\r\n        { id: 4, vector: [0.18, 0.01, 0.85, 0.80], payload: { city: 'New York' } },\r\n        { id: 5, vector: [0.24, 0.18, 0.22, 0.44], payload: { city: 'Beijing' } },\r\n        { id: 6, vector: [0.35, 0.08, 0.11, 0.44], payload: { city: 'Mumbai' } },\r\n      ],\r\n    });\r\n    return NextResponse.json({ success: true, operationInfo });\r\n  } catch (error: any) {\r\n    console.error('Qdrant POST error:', error);\r\n    return NextResponse.json({ success: false, error: error.message }, { status: 500 });\r\n  }\r\n}\r\n\r\nexport async function GET(req: NextRequest) {\r\n  try {\r\n    const searchResult = await client.query('test_collection', {\r\n      query: [0.2, 0.1, 0.9, 0.7],\r\n      limit: 3,\r\n    });\r\n    return NextResponse.json({ success: true, points: searchResult.points });\r\n  } catch (error: any) {\r\n    console.error('Qdrant GET error:', error);\r\n    return NextResponse.json({ success: false, error: error.message }, { status: 500 });\r\n  }\r\n}\r\n\r\nexport async function DELETE(req: NextRequest) {\r\n  try {\r\n    await client.deleteCollection('test_collection');\r\n    return NextResponse.json({ success: true, message: 'Collection deleted' });\r\n  } catch (error: any) {\r\n    console.error('Qdrant DELETE error:', error);\r\n    return NextResponse.json({ success: false, error: error.message }, { status: 500 });\r\n  }\r\n} "], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,2EAA2E;AAC3E,+FAA+F;AAC/F,+CAA+C;AAE/C,kCAAkC;AAClC,oCAAoC;AACpC,qCAAqC;AACrC,4BAA4B;AAC5B,+BAA+B;AAC/B,MAAM;AAEN,6CAA6C;AAC7C,MAAM,SAAS,IAAI,qLAAA,CAAA,eAAY,CAAC;IAC9B,KAAK;IACL,qBAAqB;IACrB,cAAc;IACd,QAAQ;IACR,oBAAoB;AACtB;AAEO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,IAAI;YACF,MAAM,OAAO,aAAa,CAAC;QAC7B,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,MAAM,OAAO,gBAAgB,CAAC,mBAAmB;oBAC/C,SAAS;wBAAE,MAAM;wBAAG,UAAU;oBAAM;gBACtC;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,MAAM;YACR;QACF;QACA,MAAM,gBAAgB,MAAM,OAAO,MAAM,CAAC,mBAAmB;YAC3D,MAAM;YACN,QAAQ;gBACN;oBAAE,IAAI;oBAAG,QAAQ;wBAAC;wBAAM;wBAAM;wBAAM;qBAAK;oBAAE,SAAS;wBAAE,MAAM;oBAAS;gBAAE;gBACvE;oBAAE,IAAI;oBAAG,QAAQ;wBAAC;wBAAM;wBAAM;wBAAM;qBAAK;oBAAE,SAAS;wBAAE,MAAM;oBAAS;gBAAE;gBACvE;oBAAE,IAAI;oBAAG,QAAQ;wBAAC;wBAAM;wBAAM;wBAAM;qBAAK;oBAAE,SAAS;wBAAE,MAAM;oBAAS;gBAAE;gBACvE;oBAAE,IAAI;oBAAG,QAAQ;wBAAC;wBAAM;wBAAM;wBAAM;qBAAK;oBAAE,SAAS;wBAAE,MAAM;oBAAW;gBAAE;gBACzE;oBAAE,IAAI;oBAAG,QAAQ;wBAAC;wBAAM;wBAAM;wBAAM;qBAAK;oBAAE,SAAS;wBAAE,MAAM;oBAAU;gBAAE;gBACxE;oBAAE,IAAI;oBAAG,QAAQ;wBAAC;wBAAM;wBAAM;wBAAM;qBAAK;oBAAE,SAAS;wBAAE,MAAM;oBAAS;gBAAE;aACxE;QACH;QACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM;QAAc;IAC1D,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC,GAAG;YAAE,QAAQ;QAAI;IACnF;AACF;AAEO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,MAAM,eAAe,MAAM,OAAO,KAAK,CAAC,mBAAmB;YACzD,OAAO;gBAAC;gBAAK;gBAAK;gBAAK;aAAI;YAC3B,OAAO;QACT;QACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM,QAAQ,aAAa,MAAM;QAAC;IACxE,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC,GAAG;YAAE,QAAQ;QAAI;IACnF;AACF;AAEO,eAAe,OAAO,GAAgB;IAC3C,IAAI;QACF,MAAM,OAAO,gBAAgB,CAAC;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM,SAAS;QAAqB;IAC1E,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC,GAAG;YAAE,QAAQ;QAAI;IACnF;AACF", "debugId": null}}]}