'use client'

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    Card<PERSON>ooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Lock, Contact } from "lucide-react";
import Link from "next/link";
import { useActionState } from "react";
import { Login } from "../(function)/user-action";
import { ErrorMessage, SuccessMessage } from "../(util)/messages";
import { redirect } from "next/navigation";

export default function LoginForm() {
    const [state, actionState] = useActionState(Login, undefined)
    if (state?.error) {
        ErrorMessage(state?.error);
    }
    if (state?.success) {
        SuccessMessage(state?.success);
        redirect("/dashboard/dashboard");
    }
    return (
        <form action={actionState}>
            <Card className="animate-slideInUp">
                <CardHeader>
                    <CardTitle>Login</CardTitle>
                    <CardDescription>
                        Enter your email below to login to your account.
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="compID">Company ID</Label>
                        <div className="relative">
                            <Contact className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                            <Input
                                id="compID"
                                name="compID"
                                type="text"
                                placeholder="ex.ADMN"
                                required
                                className="pl-10 uppercase"
                                defaultValue={state?.data.compID}
                            />
                        </div> 
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="password">Password</Label>
                        <div className="relative">
                            <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                            <Input name="password" id="password" type="password" defaultValue={state?.data.password} required className="pl-10" />
                        </div>
                    </div>
                </CardContent>
                <CardFooter>
                    <Button type="submit" className="w-full bg-[#278d9e]">Sign In</Button>
                </CardFooter>
                <p className="text-center text-sm text-gray-500 dark:text-gray-400">
                    Doesn't have an account? Click here to{' '}
                    <Link href="/register" className="text-[#278d9e] hover:underline font-medium">Register</Link>
                </p>
                <p className="text-center opacity-50">v1.0</p>
            </Card>
        </form>
    )
}
