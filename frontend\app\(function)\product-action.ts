'use server'

import { ErrorMessage } from "../(util)/messages";

export async function AddProduct(prevState: any, formData: FormData) {
    const pID = formData.get("pID") as string;
    const pName = formData.get("pName") as string;
    const pPrice = formData.get("pPrice") as string;
    const pDescription = formData.get("pDescription") as string;
    const pImage = formData.get("pImage") as File;

    if (!pID || !pName || !pPrice || !pImage) {
        return {
            error: "All fields are required!",
            data: { pID, pName, pPrice, pDescription }
        }
    }

    try {
        // Prepare multipart form data
        const data = new FormData();
        data.append("productID", pID);
        data.append("productName", pName);
        data.append("productPrice", pPrice);
        data.append("productDesc", pDescription);
        data.append("productImage", pImage);

        const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/product/addproduct`, {
            method: "POST",
            // Do not set Content-Type header; browser will set it for multipart
            body: data
        });

        const responseJson = await response.json();
        if (response.status.toString() === "201") {
            return {
                success: responseJson.message,
                data: { pID, pName, pPrice, pDescription }
            }
        } else {
            return {
                error: responseJson.error,
                data: { pID, pName, pPrice, pDescription }
            }
        }
    } catch (ex) {
        return {
            error: ex,
            data: { pID, pName, pPrice, pDescription }
        }
    }
}

export async function EditProduct(prevState: any, formData: FormData) {
    const pID = formData.get("pID") as string;
    const pName = formData.get("pName") as string;
    const pPrice = formData.get("pPrice") as string;
    const pDesc = formData.get("pDesc") as string;

    if (!pID || !pName || !pPrice) {
        return {
            error: "All fields are required!",
            data: { pID, pName, pPrice, pDesc }
        }
    }

    const data = new FormData();
    data.append("productID", pID);
    data.append("productName", pName);
    data.append("productPrice", pPrice);
    data.append("productDesc", pDesc);
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/product/editproduct`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            pID: pID.toUpperCase(),
            pName: pName,
            pPrice: pPrice,
            pDesc: pDesc
        })
    });

    const responseJson = await response.json();
    if (response.status.toString() === "201") {
        return {
            success: responseJson.message,
            data: { pID, pName, pPrice, pDesc }
        }
    } else {
        return {
            error: responseJson.error,
            data: { pID, pName, pPrice, pDesc }
        }
    }
}

export async function DeleteProduct(prevState: any, formData: FormData) {
    const pID = formData.get("pID") as string;
    try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/product/deleteproduct`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                pID: pID.toUpperCase(),
            })
        });

        const responseJson = await response.json();
        if (response.status.toString() === "201") {
            return {
                success: responseJson.message,
                data: { pID }
            }
        } else {
            return {
                error: responseJson.error,
                data: { pID }
            }
        }
    } catch (ex) {
        return {
            error: ex,
            data: { pID }
        }
    }
}
