import Swal from "sweetalert2";

export function ErrorMessage(message: string) {
  Swal.fire({
    title: 'Error!',
    text: message,
    icon: 'error',
    confirmButtonText: 'OK'
  })
}

export function SuccessMessage(message: string) {
  Swal.fire({
    title: 'Success!',
    text: message,
    icon: 'success',
    confirmButtonText: 'OK'
  })
}

export async function QuestionMessage(title: string, button1Text: string, button2Text: string, afterConfirmMessage: string, afterCancelMessage: string): Promise<boolean> {
  const result =  await Swal.fire({
    title: title,
    showDenyButton: false,
    showCancelButton: true,
    confirmButtonText: button1Text,
    denyButtonText: button2Text
  })

  if (result.isConfirmed) {
    Swal.fire(afterConfirmMessage, "", "success");
    return true;
  } else if (result.isDenied) {
    Swal.fire(afterCancelMessage, "", "info");
    return false;
  }
  return false;
  // .then((result) => {
  //   /* Read more about isConfirmed, isDenied below */
  //   if (result.isConfirmed) {
  //     Swal.fire(afterConfirmMessage, "", "success");
  //     return true;
  //   } else if (result.isDenied) {
  //     Swal.fire(afterCancelMessage, "", "info");
  //     return false;
  //   }
  // });
  // return false;
}