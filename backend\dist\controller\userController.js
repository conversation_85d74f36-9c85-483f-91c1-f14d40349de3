"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Login = Login;
exports.Register = Register;
const bcrypt_1 = __importDefault(require("bcrypt"));
const session_1 = require("../util/session");
const prisma_1 = require("../../generated/prisma");
const prisma = new prisma_1.PrismaClient;
function Login(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        const compID = req.body.compID;
        const password = req.body.password;
        if (!compID || !password) {
            res.status(404).json({
                error: "Missing required fields: Company ID or password"
            });
            return;
        }
        try {
            // 2. Find user by email
            // It's crucial to select the password field here, as Prisma by default
            // might not include it in the returned object for security reasons
            // if you have it configured that way.
            const user = yield prisma.user.findUnique({
                where: {
                    companyID: compID,
                },
                select: {
                    id: true,
                    companyID: true,
                    firstName: true,
                    lastName: true,
                    password: true, // Make sure to select the hashed password here
                    createdAt: true,
                    updatedAt: true,
                }
            });
            // 3. Check user existence
            if (!user) {
                // Be generic with error messages for security (e.g., don't say "email not found")
                res.status(401).json({
                    error: "Invalid credentials"
                });
                return;
            }
            // 4. Compare passwords using bcrypt.compare()
            // The first argument is the plain-text password, the second is the hashed password from the DB.
            const isPasswordValid = yield bcrypt_1.default.compare(password, user.password);
            // 5. Authenticate or Reject
            if (isPasswordValid) {
                // Passwords match! User is authenticated.
                // IMPORTANT: Remove the password hash before sending user data back
                const { password: userPasswordHash } = user, userWithoutPassword = __rest(user, ["password"]);
                // In a real application, you would typically generate a JWT or session token here
                // and send it back to the client. For this example, we'll just send user data.
                const session = (0, session_1.createSession)(Number(user.id).toString(), user.companyID);
                res.status(200)
                    // .cookie('session_token', session.token, {
                    //   httpOnly: true,
                    //   secure: process.env.NODE_ENV === 'production',
                    //   sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
                    //   maxAge: 24 * 60 * 60 * 1000, // 24 hours
                    // })
                    .json({
                    message: "Login successful",
                    user: userWithoutPassword,
                    session: session,
                });
            }
            else {
                // Passwords do not match
                res.status(401).json({
                    error: "Invalid credentials"
                });
            }
        }
        catch (error) {
            console.error("Error during user login:", error);
            res.status(500).json({
                error: "An unexpected error occurred during login.",
                details: error instanceof Error ? error.message : "Unknown error occurred"
            });
        }
    });
}
;
function Register(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        const compID = req.body.compID;
        const fName = req.body.fName;
        const lName = req.body.lName;
        const email = req.body.email;
        const password = req.body.password;
        const confimPassword = req.body.cPassword;
        if (!password || !confimPassword || !fName || !lName || !compID) {
            res.status(404).json({
                error: "Missing required fields: password, confirm passord, company ID, or name"
            });
            return;
        }
        try {
            // 2. Check if a user with the provided email already exists
            const existingUser = yield prisma.user.findUnique({
                where: {
                    companyID: compID,
                },
            });
            if (existingUser) {
                res.status(409).json({
                    error: "User with this company ID already exists."
                });
                return;
            }
            // 3. Hash the password
            const encryptedPassword = yield bcrypt_1.default.hash(password, 10);
            // 4. Create the new user
            const userCreated = yield prisma.user.create({
                data: {
                    companyID: compID.toLocaleUpperCase(),
                    firstName: fName.toLocaleUpperCase(),
                    lastName: lName.toLocaleUpperCase(),
                    password: encryptedPassword,
                },
                // You might want to select specific fields to return,
                // e.g., to avoid sending the hashed password back
                select: {
                    id: true,
                    companyID: true,
                },
            });
            // 5. Send success response
            res.status(201).json({
                message: "User created successfully",
                user: userCreated
            });
        }
        catch (error) {
            // 6. Error handling
            console.error("Error during user registration:", error);
            // This catch block will primarily handle unexpected errors from Prisma
            // or bcrypt that are not related to duplicate email (which we handled above).
            res.status(500).json({
                error: "Failed to create user due to a server error.",
                details: error instanceof Error ? error.message : "Unknown error occurred"
            });
        }
    });
}
;
