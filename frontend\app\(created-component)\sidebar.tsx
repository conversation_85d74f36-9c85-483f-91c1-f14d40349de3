"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { redirect, usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  Settings,
  LogOut,
  BarChart,
  ChevronLeft,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { DeleteCookie } from "../(function)/cookie";

export default function Sidebar() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const navItems = [
    { href: "/dashboard/dashboard", icon: LayoutDashboard, label: "Dashboard" },
    { href: "/dashboard/product", icon: Package, label: "Products" },
    { href: "/dashboard/order", icon: ShoppingCart, label: "Orders" },
    { href: "/dashboard/customer", icon: Users, label: "Customers" },
    { href: "/dashboard/analytic", icon: Bar<PERSON>hart, label: "Analytics" },
  ];

  return (
    <aside
      className={cn(
        "flex flex-col py-8 bg-white border-r dark:bg-gray-900 dark:border-gray-800 transition-all duration-300 ease-in-out",
        isCollapsed ? "w-20 px-2" : "w-64 px-4"
      )}
    >
      <div className="relative flex flex-col items-center">
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="absolute -right-2 top-0 p-1.5 text-gray-600 transition-all duration-300 transform bg-gray-100 rounded-full shadow-md dark:bg-gray-800 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
        >
          <ChevronLeft
            className={cn(
              "w-5 h-5 transition-transform",
              isCollapsed && "rotate-180"
            )}
          />
        </button>
        <Image
          src="/logo/ddc_logo.png"
          alt="Sapwir Snackies Logo"
          width={80}
          height={80}
          className={cn("transition-all", isCollapsed && "w-12 h-12")}
        />
        <h1
          className={cn(
            "mt-2 text-2xl font-bold bg-gradient-to-r from-[#278d9e] to-[#60c4d5] bg-clip-text text-transparent transition-opacity",
            isCollapsed && "opacity-0 h-0 pointer-events-none"
          )}
        >
          Sapwir Snackies
        </h1>
      </div>

      <div className="flex flex-col justify-between flex-1 mt-6">
        <nav className="flex-grow overflow-y-auto">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center py-2 mt-2 text-gray-600 transition-colors duration-300 transform rounded-lg dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-200",
                isCollapsed ? "px-2 justify-center" : "px-4",
                pathname === item.href &&
                  "bg-[#278d9e] text-white dark:bg-[#278d9e] hover:bg-[#278d9e]/90 dark:hover:bg-[#278d9e]/90"
              )}
            >
              <item.icon className="w-5 h-5" />
              <span className={cn("mx-4 font-medium", isCollapsed && "hidden")}>
                {item.label}
              </span>
            </Link>
          ))}
        </nav>

        <div>
          <Link
            href="/settings"
            className={cn(
              "flex items-center py-2 mt-2 text-gray-600 transition-colors duration-300 transform rounded-lg dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-200",
              isCollapsed ? "px-2 justify-center" : "px-4",
              pathname === "/dashboard/settings" &&
                "bg-[#278d9e] text-white dark:bg-[#278d9e] hover:bg-[#278d9e]/90 dark:hover:bg-[#278d9e]/90"
            )}
          >
            <Settings className="w-5 h-5" />
            <span className={cn("mx-4 font-medium", isCollapsed && "hidden")}>
              Settings
            </span>
          </Link>

          <Link
            href="/login"
            onClick={(e) =>{
              e.preventDefault();
              DeleteCookie();
              redirect("/login");
            }}
            className={cn(
              "flex items-center py-2 mt-2 text-gray-600 transition-colors duration-300 transform rounded-lg dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-200",
              isCollapsed ? "px-2 justify-center" : "px-4"
            )}
          >
            <LogOut className="w-5 h-5" />
            <span className={cn("mx-4 font-medium", isCollapsed && "hidden")}>
              Logout
            </span>
          </Link>
        </div>
      </div>
    </aside>
  );
}
