import { Request, Response } from "express";
import { PrismaClient } from "../../generated/prisma";
import path from "path";
import fs from "fs";

const prisma = new PrismaClient();

// Extend Request type to include file (with any type to avoid linter issues)
interface MulterRequest extends Request {
    file?: any;
}

export async function AddProduct(req: MulterRequest, res: Response): Promise<void> {
    const productID = req.body.productID as string
    const productName = req.body.productName as string
    const productPrice = req.body.productPrice as string
    const productDesc = req.body.productDesc as string
    // Get file from multer
    const file = req.file;
    let productImage = "";
    let filePath = "";
    if (file) {
        // Save file to ../frontend/public/product
        const publicDir = path.resolve(__dirname, '../../../frontend/public/product');
        if (!fs.existsSync(publicDir)) {
            fs.mkdirSync(publicDir, { recursive: true });
        }
        filePath = path.join(publicDir, file.originalname);
        fs.writeFileSync(filePath, file.buffer);
        productImage = file.originalname;
    } else {
        res.status(400).json({ error: "Product image is required." });
        return;
    }

    if (!productID || !productName || !productPrice || !productImage) {
        console.log(productID + " " + productName + " " + productPrice + " " + productImage);
        res.status(404).json({
            error: "Missing required fields: product ID, name, price, description, or image"
        });
        return;
    }

    try {
        const productCreated = await prisma.product.create({
            data: {
                productID: productID.toUpperCase(),
                productName: productName.toUpperCase(),
                productPrice: parseInt(productPrice),
                productDesc: productDesc,
                productImage: file.originalname,
            },
            select: {
                id: true,
                productID: true,
            },
        });

        res.status(201).json({
            message: "Product created successfully",
            product: productCreated
        });
        return;
    } catch (error) {
        console.error("Error during product creation:", error);
        res.status(500).json({
            error: "Failed to create product due to a server error.",
            details: error instanceof Error ? error.message : "Unknown error occurred"
        });
        return;
    }
}

export async function EditProduct(req: Request, res: Response): Promise<void> {
    const productID = req.body.pID as string
    const productName = req.body.pName as string
    const productPrice = req.body.pPrice as string
    const productDesc = req.body.pDesc as string

    if (!productID || !productName || !productPrice) {
        res.status(404).json({
            error: "Missing required fields: product ID, name, price or description"
        });
        return;
    }

    try {
        const editedProduct = await prisma.product.update({
            data: {
                productID: productID,
                productName: productName,
                productPrice: Number(productPrice),
                productDesc: productDesc
            },
            where: {
                productID: productID
            }
        });

        res.status(201).json({
            message: `Product ${productID.toUpperCase()} edited successfully`,
            product: editedProduct
        });
    } catch (error) {
        console.error("Error during product edit:", error);
        res.status(500).json({
            error: "Failed to edit product due to a server error.",
            details: error instanceof Error ? error.message : "Unknown error occurred"
        });
        return;
    }
}


export async function DeleteProduct(req: Request, res: Response): Promise<void> {
    const productID = req.body.pID as string

    try {
        const deletedProduct = await prisma.product.update({
            data: {
                archive: true
            },
            where: {
                productID: productID
            }
        });

        res.status(201).json({
            message: `Product ${productID.toUpperCase()} deleted successfully`,
            product: deletedProduct
        });
    } catch (error) {
        console.error("Error during product delete:", error);
        res.status(500).json({
            error: "Failed to delete product due to a server error.",
            details: error instanceof Error ? error.message : "Unknown error occurred"
        });
        return;
    }
}

export async function GetAllProducts(req: Request, res: Response) {
    const data = await prisma.product.findMany({
        select: {
            id: true,
            productID: true,
            productName: true,
            productPrice: true,
            productDesc: true,
            productImage: true,
            addedDate: true,
        },
        where: {
            archive: false
        }
    });

    res.status(200).json(data);
}