'use client'

import { Contact, Mail, Lock, User } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useActionState, useEffect } from "react";
import { Register } from "../(function)/user-action";
import { ErrorMessage, SuccessMessage } from "../(util)/messages";
import { redirect } from "next/navigation";

export default function RegisterForm() {
    const [state, ActionState] = useActionState(Register, undefined);

    useEffect(() => {
        if (state?.error) {
            ErrorMessage(state?.error);
        } 
        if (state?.success) {
            SuccessMessage(state?.success);
            redirect("/login");
        }
    }, [state]);

    return (
        <form action={ActionState}>
            <Card className="animate-slideInUp">
                <CardHeader>
                    <CardTitle>Register</CardTitle>
                    <CardDescription>
                        Fill in the details below to create your account.
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex gap-2">
                        <div className="space-y-2">
                            <Label htmlFor="compID">Company ID</Label>
                            <div className="relative">
                                <Contact className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                <Input
                                    id="compID"
                                    name="compID"
                                    type="text"
                                    placeholder="ex.ADMN"
                                    className="pl-10 uppercase"
                                    defaultValue={state?.data?.compID}
                                />
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="email">Email</Label>
                            <div className="relative">
                                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                <Input
                                    id="email"
                                    name="email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                    className="pl-10"
                                    defaultValue={state?.data?.email}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <div className="space-y-2">
                            <Label htmlFor="password">Password</Label>
                            <div className="relative">
                                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                <Input name="password" id="password" defaultValue={state?.data?.password} type="password" required className="pl-10" />
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="confirmPassword">Confirm Password</Label>
                            <div className="relative">
                                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                <Input name="confirmPassword" id="confirmPassword" defaultValue={state?.data?.cPassword} type="password" required className="pl-10" />
                            </div>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <div className="space-y-2">
                            <Label htmlFor="fName">First Name</Label>
                            <div className="relative">
                                <User className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                <Input name="fName" id="fName" defaultValue={state?.data?.fName} type="text" required className="pl-10 uppercase" />
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="lName">Last Name</Label>
                            <div className="relative">
                                <User className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                <Input name="lName" id="lName" defaultValue={state?.data?.lName} type="text" required className="pl-10 uppercase" />
                            </div>
                        </div>
                    </div>
                </CardContent>
                <CardFooter className="flex flex-col gap-2">
                    <Button type="submit" className="w-full bg-[#278d9e] hover:bg-[#206b77] text-white font-semibold">Register</Button>
                    <p className="text-center text-sm text-gray-500 dark:text-gray-400">
                        Already have an account?{' '}
                        <Link href="/login" className="text-[#278d9e] hover:underline font-medium">Login</Link>
                    </p>
                </CardFooter>
                <p className="text-center opacity-50">v1.0</p>
            </Card>
        </form>
    )
}
