import { Request, Response } from "express";
import bcrypt from "bcrypt";
import { createSession } from "../util/session";
import { PrismaClient } from "../../generated/prisma";

const prisma = new PrismaClient();
export async function Login(req: Request, res: Response) {
  const compID = req.body.compID as string;
  const password = req.body.password as string;

  if (!compID || !password) {
    res.status(404).json({
      error: "Missing required fields: Company ID or password"
    });
    return
  }

  try {
    // 1. Find user by companyID, only select id and password for efficiency
    const user = await prisma.user.findUnique({
      where: { companyID: compID },
      select: {
        id: true,
        password: true
      }
    });

    if (!user) {
      res.status(401).json({ error: "Invalid credentials" });
      return
    }

    // 2. Compare passwords
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      res.status(401).json({ error: "Invalid credentials" });
      return
    }

    // 3. Fetch additional user info (excluding password)
    const userInfo = await prisma.user.findUnique({
      where: { companyID: compID },
      select: {
        id: true,
        companyID: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    // 4. Create session
    const session = createSession(String(userInfo?.id), userInfo?.companyID as string);

    res.status(201).json({
      message: "Login successful",
      user: userInfo,
      session: session,
    });
    // res.send("success");
  } catch (error) {
    console.error("Error during user login:", error);
    res.status(500).json({
      error: "An unexpected error occurred during login.",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    });
  }
}

export async function Register(req: Request, res: Response) {
  const compID = req.body.compID as string
  const fName = req.body.fName as string
  const lName = req.body.lName as string
  const email = req.body.email as string
  const password = req.body.password as string
  const confimPassword = req.body.cPassword as string

  if (!password || !confimPassword || !fName || !lName || !compID) {
    res.status(404).json({
      error: "Missing required fields: password, confirm passord, company ID, or name"
    });
    return
  }

  try {
    // 2. Check if a user with the provided email already exists
    const existingUser = await prisma.user.findUnique({
      where: {
        companyID: compID,
      },
    });

    if (existingUser) {
      res.status(409).json({ // 409 Conflict for duplicate resource
        error: "User with this company ID already exists."
      });
      return
    }

    // 3. Hash the password
    const encryptedPassword = await bcrypt.hash(password, 10);

    // 4. Create the new user
    const userCreated = await prisma.user.create({
      data: {
        companyID: compID.toLocaleUpperCase(),
        firstName: fName.toLocaleUpperCase(),
        lastName: lName.toLocaleUpperCase(),
        password: encryptedPassword,
      },
      // You might want to select specific fields to return,
      // e.g., to avoid sending the hashed password back
      select: {
        id: true,
        companyID: true,
      },
    });

    // 5. Send success response
    res.status(201).json({
      message: "User created successfully",
      user: userCreated
    });
  } catch (error) {
    // 6. Error handling
    console.error("Error during user registration:", error);

    // This catch block will primarily handle unexpected errors from Prisma
    // or bcrypt that are not related to duplicate email (which we handled above).
    res.status(500).json({
      error: "Failed to create user due to a server error.",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    });
  }
};
