// If your Qdrant server is running with HTTPS, use api<PERSON><PERSON> and https://host
// If your Qdrant server is running locally with HTTP, do NOT use api<PERSON>ey and use only host/port
// Uncomment the correct config for your setup:

// For HTTPS Qdrant (e.g., cloud):
// const client = new QdrantClient({
//   url: 'https://your-qdrant-host',
//   apiKey: 'your_api_key',
//   checkCompatibility: false,
// });

// For local HTTP Qdrant (no apiKey, no SSL):
const client = new QdrantClient({
  url: "http://*************:20311/qdrant/api/gateway",
  // host: 'localhost',
  // port: 6333,
  apiKey: "SampleQdrantKey2025",
  checkCompatibility: false,
});

import { NextRequest, NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';

export async function POST(req: NextRequest) {
  try {
    try {
      await client.getCollection('test_collection');
    } catch (error: any) {
      if (error.status === 404) {
        await client.createCollection('test_collection', {
          vectors: { size: 4, distance: 'Dot' },
        });
      } else {
        console.error('Qdrant POST inner error:', error);
        throw error;
      }
    }
    const operationInfo = await client.upsert('test_collection', {
      wait: true,
      points: [
        { id: 1, vector: [0.05, 0.61, 0.76, 0.74], payload: { city: 'Berlin' } },
        { id: 2, vector: [0.19, 0.81, 0.75, 0.11], payload: { city: 'London' } },
        { id: 3, vector: [0.36, 0.55, 0.47, 0.94], payload: { city: 'Moscow' } },
        { id: 4, vector: [0.18, 0.01, 0.85, 0.80], payload: { city: 'New York' } },
        { id: 5, vector: [0.24, 0.18, 0.22, 0.44], payload: { city: 'Beijing' } },
        { id: 6, vector: [0.35, 0.08, 0.11, 0.44], payload: { city: 'Mumbai' } },
      ],
    });
    return NextResponse.json({ success: true, operationInfo });
  } catch (error: any) {
    console.error('Qdrant POST error:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  try {
    const searchResult = await client.query('test_collection', {
      query: [0.2, 0.1, 0.9, 0.7],
      limit: 3,
    });
    return NextResponse.json({ success: true, points: searchResult.points });
  } catch (error: any) {
    console.error('Qdrant GET error:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    await client.deleteCollection('test_collection');
    return NextResponse.json({ success: true, message: 'Collection deleted' });
  } catch (error: any) {
    console.error('Qdrant DELETE error:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
} 