"use client";

import React, { ReactNode, useState } from "react";
import Sidebar from "../(created-component)/sidebar";
import { Menu } from "lucide-react";
import { cn } from "@/lib/utils";

export default function DashboardLayout({ content }: { content: ReactNode }) {
  const [isSidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      {/* Desktop Sidebar */}
      <div className="hidden md:flex">
        <Sidebar />
      </div>

      {/* Mobile Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 w-64 transition-transform duration-300 ease-in-out md:hidden",
          isSidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <Sidebar />
      </div>
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black opacity-50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}

      <div className="flex flex-col flex-1 overflow-y-auto">
        <header className="flex items-center justify-between p-4 bg-white shadow-md md:hidden dark:bg-gray-900">
          <button
            onClick={() => setSidebarOpen(true)}
            className="p-2 text-gray-500 rounded-md hover:text-gray-700 hover:bg-gray-100"
          >
            <Menu className="w-6 h-6" />
          </button>
          <h1 className="text-xl font-bold bg-gradient-to-r from-[#278d9e] to-[#60c4d5] bg-clip-text text-transparent">
            Sapwir Snackies
          </h1>
        </header>
        <main className="flex-1 p-4 md:p-8">{content}</main>
      </div>
    </div>
  );
}
