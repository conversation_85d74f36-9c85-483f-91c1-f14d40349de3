import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { jwtVerify } from 'jose'

// This function can be marked `async` if using `await` inside
export async function middleware(request: NextRequest) {
    // // Get the token from the cookies
    // const token = request.cookies.get('session')?.value
    // const isAuthPage = request.nextUrl.pathname === '/login' || request.nextUrl.pathname === '/register'

    // // If user is on auth page and has valid token, redirect to home
    // if (isAuthPage && token) {
    //     try {
    //         const secret = process.env.JWT_SECRET
    //         if (!secret) {
    //             throw new Error('JWT_SECRET is not defined')
    //         }

    //         const secretKey = new TextEncoder().encode(secret)
    //         await jwtVerify(token, secretKey)

    //         // Token is valid, redirect to home
    //         // return NextResponse.redirect(new URL('/dashboard/dashboard', request.url))
    //         return NextResponse.next();
    //     } catch (error) {
    //         // Token is invalid, clear it and continue to auth page
    //         const response = NextResponse.next()
    //         response.cookies.delete('session')
    //         return response
    //     }
    // }

    // // If there's no token and not on auth page, redirect to login
    // if (!token && !isAuthPage) {
    //     return NextResponse.redirect(new URL('/login', request.url))
    // }

    // // If there's a token, verify it
    // if (token && !isAuthPage) {
    //     try {
    //         const secret = process.env.JWT_SECRET
    //         if (!secret) {
    //             throw new Error('JWT_SECRET is not defined')
    //         }

    //         const secretKey = new TextEncoder().encode(secret)
    //         await jwtVerify(token, secretKey)

    //         // If token is valid, allow the request to proceed
    //         return NextResponse.next()
    //     } catch (error) {
    //         // If token is invalid, redirect to login
    //         const response = NextResponse.redirect(new URL('/login', request.url))
    //         response.cookies.delete('session')
    //         return response
    //     }
    // }

    // // Allow access to auth pages if no token
    // return NextResponse.next()
}

// Configure which routes to run the middleware on
export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         */
        '/((?!api|_next/static|_next/image|favicon.ico).*)',
    ],
}
