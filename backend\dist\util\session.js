"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.renewSession = exports.validateSession = exports.createSession = void 0;
const jwtSession_1 = require("./jwtSession");
const createSession = (userId, email) => {
    const token = (0, jwtSession_1.generateToken)(userId, email);
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours from now
    return {
        token,
        userId,
        email,
        createdAt: now,
        expiresAt
    };
};
exports.createSession = createSession;
const validateSession = (token) => {
    const payload = (0, jwtSession_1.verifyToken)(token);
    if (!payload) {
        return null;
    }
    return {
        token,
        userId: payload.userId,
        email: payload.email,
        createdAt: new Date(), // Note: We don't have the original creation time
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    };
};
exports.validateSession = validateSession;
const renewSession = (token) => {
    const newToken = (0, jwtSession_1.refreshToken)(token);
    if (!newToken) {
        return null;
    }
    const payload = (0, jwtSession_1.verifyToken)(newToken);
    if (!payload) {
        return null;
    }
    return {
        token: newToken,
        userId: payload.userId,
        email: payload.email,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    };
};
exports.renewSession = renewSession;
