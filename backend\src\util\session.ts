import { generateToken, verifyToken, refreshToken } from './jwtSession';

interface SessionData {
  token: string;
  userId: string;
  companyID: string;
  createdAt: Date;
  expiresAt: Date;
}

export const createSession = (userId: string, companyID: string): SessionData => {
  const token = generateToken(userId, companyID);
  const now = new Date();
  const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours from now

  return {
    token,
    userId,
    companyID,
    createdAt: now,
    expiresAt
  };
};

export const validateSession = (token: string): SessionData | null => {
  const payload = verifyToken(token);
  if (!payload) {
    return null;
  }

  return {
    token,
    userId: payload.userId,
    companyID: payload.companyID,
    createdAt: new Date(), // Note: We don't have the original creation time
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
  };
};

export const renewSession = (token: string): SessionData | null => {
  const newToken = refreshToken(token);
  if (!newToken) {
    return null;
  }

  const payload = verifyToken(newToken);
  if (!payload) {
    return null;
  }

  return {
    token: newToken,
    userId: payload.userId,
    companyID: payload.companyID,
    createdAt: new Date(),
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
  };
};
