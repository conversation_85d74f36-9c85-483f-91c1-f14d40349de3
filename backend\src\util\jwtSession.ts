// src/utils/jwt.ts
import jwt from 'jsonwebtoken';

// Ensure you have a JWT_SECRET in your .env file
// And make sure your environment variables are loaded (e.g., using 'dotenv' package)
// const JWT_SECRET = process.env.JWT_SECRET as jwt.Secret;
const JWT_SECRET = "SapwirSnackies2025";

if (!JWT_SECRET) {
  console.error('FATAL ERROR: JWT_SECRET is not defined. Please set it in your .env file.');
  // In a real application, you might want to exit the process or throw an error
  // process.exit(1);
}

// Optionally, define an expiration time for your tokens
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1d'; // e.g., '1h', '7d'

interface JwtPayload {
  userId: string;
  companyID: string;
  // Add any other non-sensitive data you want to store in the token
}

export const generateToken = (userId: string, companyID: string): string => {
  if (!JWT_SECRET) {
    throw new Error('JWT_SECRET is not defined.');
  }
  const payload: JwtPayload = { userId, companyID };
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN } as jwt.SignOptions);
};

export const verifyToken = (token: string): JwtPayload | null => {
  if (!JWT_SECRET) {
    throw new Error('JWT_SECRET is not defined.');
  }
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload;
    return decoded;
  } catch (error) {
    console.error('JWT verification failed:', error);
    return null;
  }
};

export const refreshToken = (token: string): string | null => {
  const payload = verifyToken(token);
  if (!payload) {
    return null;
  }
  return generateToken(payload.userId, payload.companyID);
};