
import Image from "next/image";
import RegisterForm from "../(created-component)/register-form";

export default function RegisterPage() {
    return (
        <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 animate-fadeIn">
            <div className="w-full max-w-md p-8 space-y-8 transform transition-all duration-500 ease-in-out">
                <div className="text-center">
                    <Image
                        src="/logo/ddc_logo.png"
                        alt="Sapwir Snackies Logo"
                        width={100}
                        height={100}
                        className="mx-auto"
                    />
                    <h1 className="mt-6 text-4xl font-bold tracking-tight text-gray-900 dark:text-gray-50 sm:text-5xl animate-slideInDown">
                        Sapwir Snackies
                    </h1>
                    <p className="mt-4 text-lg text-gray-600 dark:text-gray-400 animate-slideInUp">
                        Create your account and join the snack revolution!
                    </p>
                </div>
                <RegisterForm />
            </div>
        </div>
    );
}
